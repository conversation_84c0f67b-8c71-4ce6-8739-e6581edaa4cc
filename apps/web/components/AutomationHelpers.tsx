'use client'

import React, { useState } from 'react'

interface AutomationHelper {
  id: string
  name: string
  description: string
  enabled: boolean
  tooltip: string
}

interface AutomationHelpersProps {
  className?: string
  onToggle?: (helperId: string, enabled: boolean) => void
}

const zenTheme = {
  surface: '#FFFFFF',
  border: '#E5E7EB',
  primaryText: '#0B0F14',
  secondaryText: '#475569',
  accent: '#111827',
  accentBlue: '#2563EB',
  success: '#059669',
  shadow: '0 1px 2px rgba(0, 0, 0, 0.08)',
}

export default function AutomationHelpers({
  className = '',
  onToggle,
}: AutomationHelpersProps) {
  const [helpers, setHelpers] = useState<AutomationHelper[]>([
    {
      id: 'invoice-extractor',
      name: 'Invoice Reader',
      description: 'Automatically read and extract data from uploaded invoices',
      enabled: true,
      tooltip: 'Uses AI to extract invoice details like amounts, dates, and VAT information from PDFs and images',
    },
    {
      id: 'bank-reconciler',
      name: 'Smart Matching',
      description: 'Match bank transactions to invoices automatically',
      enabled: true,
      tooltip: 'Automatically suggests matches between bank transactions and your invoices or journal entries',
    },
    {
      id: 'vat-copilot',
      name: 'VAT Assistant',
      description: 'Real-time VAT position tracking and calculations',
      enabled: true,
      tooltip: 'Continuously calculates your VAT position and helps ensure compliance with Belgian VAT requirements',
    },
    {
      id: 'compliance-reminders',
      name: 'Deadline Alerts',
      description: 'Remind you of important deadlines and requirements',
      enabled: false,
      tooltip: 'Get notified about VAT filing deadlines, invoice due dates, and other important business dates',
    },
  ])

  const handleToggle = (helperId: string) => {
    setHelpers(prev =>
      prev.map(helper =>
        helper.id === helperId
          ? { ...helper, enabled: !helper.enabled }
          : helper
      )
    )
    
    const helper = helpers.find(h => h.id === helperId)
    if (helper) {
      onToggle?.(helperId, !helper.enabled)
    }
  }

  const getPanelStyle = () => ({
    background: zenTheme.surface,
    border: `1px solid ${zenTheme.border}`,
    borderRadius: '8px',
    padding: '24px',
    boxShadow: zenTheme.shadow,
  })

  const getTitleStyle = () => ({
    fontSize: '20px',
    fontWeight: 600,
    color: zenTheme.primaryText,
    margin: '0 0 8px 0',
  })

  const getSubtitleStyle = () => ({
    fontSize: '14px',
    color: zenTheme.secondaryText,
    margin: '0 0 24px 0',
    lineHeight: 1.4,
  })

  const getHelperCardStyle = (isLast: boolean) => ({
    padding: '16px 0',
    borderBottom: isLast ? 'none' : `1px solid ${zenTheme.border}`,
  })

  const getHelperHeaderStyle = () => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '8px',
  })

  const getHelperNameStyle = () => ({
    fontSize: '16px',
    fontWeight: 500,
    color: zenTheme.primaryText,
    margin: 0,
  })

  const getHelperDescriptionStyle = () => ({
    fontSize: '14px',
    color: zenTheme.secondaryText,
    margin: 0,
    lineHeight: 1.4,
  })

  const getToggleContainerStyle = () => ({
    position: 'relative' as const,
    display: 'inline-block',
  })

  const getToggleStyle = (enabled: boolean) => ({
    width: '44px',
    height: '24px',
    backgroundColor: enabled ? zenTheme.success : '#D1D5DB',
    borderRadius: '12px',
    border: 'none',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
    position: 'relative' as const,
    outline: 'none',
  })

  const getToggleKnobStyle = (enabled: boolean) => ({
    width: '20px',
    height: '20px',
    backgroundColor: 'white',
    borderRadius: '50%',
    position: 'absolute' as const,
    top: '2px',
    left: enabled ? '22px' : '2px',
    transition: 'left 0.2s ease',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  })

  const getTooltipStyle = () => ({
    position: 'absolute' as const,
    bottom: '100%',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: zenTheme.accent,
    color: 'white',
    padding: '8px 12px',
    borderRadius: '6px',
    fontSize: '12px',
    whiteSpace: 'nowrap' as const,
    zIndex: 10,
    marginBottom: '8px',
    maxWidth: '200px',
    whiteSpace: 'normal' as const,
    textAlign: 'center' as const,
  })

  return (
    <div className={className} style={getPanelStyle()}>
      <h2 style={getTitleStyle()}>Automation Helpers</h2>
      <p style={getSubtitleStyle()}>
        Smart features that work in the background to save you time
      </p>

      <div>
        {helpers.map((helper, index) => (
          <div
            key={helper.id}
            style={getHelperCardStyle(index === helpers.length - 1)}
          >
            <div style={getHelperHeaderStyle()}>
              <h3 style={getHelperNameStyle()}>{helper.name}</h3>
              <div style={getToggleContainerStyle()}>
                <button
                  style={getToggleStyle(helper.enabled)}
                  onClick={() => handleToggle(helper.id)}
                  aria-label={`Toggle ${helper.name}`}
                  title={helper.tooltip}
                >
                  <div style={getToggleKnobStyle(helper.enabled)} />
                </button>
              </div>
            </div>
            <p style={getHelperDescriptionStyle()}>{helper.description}</p>
          </div>
        ))}
      </div>

      <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#F9FAFB', borderRadius: '6px' }}>
        <p style={{ fontSize: '12px', color: zenTheme.secondaryText, margin: 0, textAlign: 'center' }}>
          💡 These features run automatically when enabled. You can toggle them on or off anytime.
        </p>
      </div>
    </div>
  )
}
